#!/usr/bin/env python3
"""
Test script for the download refactoring.
This script tests that the prepare_ip_downloads function works correctly
and that the refactored Do_Check_* functions still work as expected.
"""

import os
import asyncio
import tempfile
import shutil
from unittest.mock import AsyncMock, patch
from Check.RAG.qdrant_search import prepare_ip_downloads


async def test_prepare_ip_downloads_copyright():
    """Test prepare_ip_downloads for copyright results."""
    print("Testing prepare_ip_downloads for Copyright...")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Mock copyright result
        copyright_result = {
            'ip_type': 'Copyright',
            'plaintiff_id': '123',
            'ip_image': ['test_image.jpg', 'test_cert.pdf'],
            'ip_owner': 'Test Owner',
            'reg_no': 'TX123456'
        }
        
        # Mock the download_from_url function to return True (success)
        with patch('Check.RAG.qdrant_search.download_from_url', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = True
            
            # Test the function
            enhanced_results = await prepare_ip_downloads([copyright_result], temp_dir, "test_check")
            
            # Verify results
            assert len(enhanced_results) == 1
            result = enhanced_results[0]
            
            # Check that download information was added (unified structure - lists for all IP types)
            assert 'IP_Urls' in result
            assert 'ip_local_paths' in result
            assert 'download_statuses' in result

            # Check URL format
            expected_url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/123/high/test_image.jpg"
            assert result['IP_Urls'] == [expected_url]

            # Check local path
            expected_path = os.path.join(temp_dir, 'test_image.jpg')
            assert result['ip_local_paths'] == [expected_path]
            
            # Check download was called
            mock_download.assert_called_once_with(expected_url, expected_path)
            
            print("✅ Copyright test passed!")


async def test_prepare_ip_downloads_patent():
    """Test prepare_ip_downloads for patent results."""
    print("Testing prepare_ip_downloads for Patent...")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Mock patent result
        patent_result = {
            'ip_type': 'Patent',
            'plaintiff_id': '456',
            'ip_image': ['patent_page1.jpg', 'patent_page2.jpg', 'patent_cert.pdf'],
            'ip_owner': 'Test Patent Owner',
            'reg_no': 'US123456'
        }
        
        # Mock the download_from_url function to return True (success)
        with patch('Check.RAG.qdrant_search.download_from_url', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = True
            
            # Test the function
            enhanced_results = await prepare_ip_downloads([patent_result], temp_dir, "test_check")
            
            # Verify results
            assert len(enhanced_results) == 1
            result = enhanced_results[0]
            
            # Check that download information was added (unified structure - lists for all IP types)
            assert 'IP_Urls' in result
            assert 'ip_local_paths' in result
            assert 'download_statuses' in result
            
            # Check URLs format
            expected_urls = [
                f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/456/high/patent_page1.jpg",
                f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/456/high/patent_page2.jpg",
                f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/456/high/patent_cert.pdf"
            ]
            assert result['IP_Urls'] == expected_urls
            
            # Check local paths
            expected_paths = [
                os.path.join(temp_dir, 'patent_page1.jpg'),
                os.path.join(temp_dir, 'patent_page2.jpg'),
                os.path.join(temp_dir, 'patent_cert.pdf')
            ]
            assert result['ip_local_paths'] == expected_paths
            
            # Check download was called for each image
            assert mock_download.call_count == 3
            
            print("✅ Patent test passed!")


async def test_prepare_ip_downloads_trademark():
    """Test prepare_ip_downloads for trademark results."""
    print("Testing prepare_ip_downloads for Trademark...")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Mock trademark result
        trademark_result = {
            'ip_type': 'Trademark',
            'plaintiff_id': '789',
            'ip_image': ['trademark_image.jpg', 'trademark_cert.pdf'],
            'ip_owner': 'Test Trademark Owner',
            'reg_no': 'TM123456'
        }
        
        # Mock the download_from_url function to return True (success)
        with patch('Check.RAG.qdrant_search.download_from_url', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = True
            
            # Test the function
            enhanced_results = await prepare_ip_downloads([trademark_result], temp_dir, "test_check")
            
            # Verify results
            assert len(enhanced_results) == 1
            result = enhanced_results[0]
            
            # Check that download information was added (unified structure - lists for all IP types)
            assert 'IP_Urls' in result
            assert 'ip_local_paths' in result
            assert 'download_statuses' in result

            # Check URL format (trademark uses second image - certificate)
            expected_url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/789/high/trademark_cert.pdf"
            assert result['IP_Urls'] == [expected_url]

            # Check local path
            expected_path = os.path.join(temp_dir, 'trademark_cert.pdf')
            assert result['ip_local_paths'] == [expected_path]
            
            # Check download was called
            mock_download.assert_called_once_with(expected_url, expected_path)
            
            print("✅ Trademark test passed!")


async def test_prepare_ip_downloads_failure():
    """Test prepare_ip_downloads when download fails."""
    print("Testing prepare_ip_downloads with download failure...")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Mock copyright result
        copyright_result = {
            'ip_type': 'Copyright',
            'plaintiff_id': '123',
            'ip_image': ['test_image.jpg'],
            'ip_owner': 'Test Owner',
            'reg_no': 'TX123456'
        }
        
        # Mock the download_from_url function to return False (failure)
        with patch('Check.RAG.qdrant_search.download_from_url', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = False
            
            # Mock the error logging
            with patch('Check.RAG.qdrant_search._log_download_error', new_callable=AsyncMock) as mock_log_error:
                # Test the function
                enhanced_results = await prepare_ip_downloads([copyright_result], temp_dir, "test_check")
                
                # Verify that failed downloads are filtered out
                assert len(enhanced_results) == 0
                
                # Verify error was logged
                mock_log_error.assert_called_once_with("test_check", mock_download.call_args[0][0])
                
                print("✅ Download failure test passed!")


async def main():
    """Main function to run all tests."""
    print("Running download refactoring tests...\n")
    
    try:
        await test_prepare_ip_downloads_copyright()
        await test_prepare_ip_downloads_patent()
        await test_prepare_ip_downloads_trademark()
        await test_prepare_ip_downloads_failure()
        
        print("\n🎉 All tests passed! The download refactoring is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
