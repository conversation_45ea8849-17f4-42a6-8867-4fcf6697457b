from AI.LLM_shared import get_json, get_report
from AI.GC_VertexAI import vertex_genai_multi_async
from Check.Find_In_List import find_brand_in_trademark_text, find_name_in_plaintiff_list, find_brand_in_text
from langfuse import observe
import langfuse
from FileManagement.Tencent_COS import async_upload_file_with_retry
from Check.Do_Check_Download import download_from_url
from AI.GCV_GetImageParts import get_image_parts_async
from Check.RAG.npy_trademark import find_most_similar_trademark_logo
import json
import shutil
import time
import os
import asyncio
import Common.Constants as Constants


@observe()
async def process_single_image(query_image_path, client, bucket, temp_dir, check_id, plaintiff_df, query_image_urls):
    prompt_brand = f'Detect all of the Trademarks of generally known brands. For each, assign a label that includes the type of trademark (text or logo), the brand name and the parent company in this format: "Type, Brand Name, Parent Company"'

    # For Langfuse
    # product_url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/{os.path.basename(query_image_path)}"
    product_url = query_image_urls[os.path.basename(query_image_path)]
    image_url = ["", product_url.replace(" ", "%20").replace("http", "https")]

    # We make a copy just for trademark because copyright will also create parts and it should not overwrite the copyright parts
    query_image_path_old = query_image_path
    query_image_path = os.path.join(os.path.dirname(query_image_path), "trademark", os.path.basename(query_image_path))
    os.makedirs(os.path.dirname(query_image_path), exist_ok=True)
    shutil.copy(query_image_path_old, query_image_path)
    images_parts = await get_image_parts_async(prompt_brand, query_image_path, image_url, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX) #, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX)

    results = []
    for image_part in images_parts:
        label_parts = image_part["label"].split(",")
        label_type = label_parts[0].lower().strip() if len(label_parts) >= 3 else "unknown"
        brand_name = label_parts[1].strip() if len(label_parts) >= 3 else ""
        parent_company = label_parts[2].strip() if len(label_parts) >= 3 else ""
        if label_type == "logo":
            results.extend(await process_brand_logo(query_image_path, image_part["path"], product_url, brand_name, parent_company, client, bucket, temp_dir, check_id, plaintiff_df))
        elif label_type == "text":
            results.extend(await process_brand_text(query_image_path, product_url, brand_name, parent_company, client, bucket, temp_dir, check_id, plaintiff_df))
        else:
            print(f"Trademark: Unknown label: {image_part['label']}")

    langfuse.get_client().update_current_span(input=f'\n![Alt text]({product_url.replace(" ", "%20").replace("http", "https")})')

    # await upload_task
    return results

@observe()
async def process_brand_logo(query_image_path, query_image_part_path, product_url, brand_name, parent_company, client, bucket, temp_dir, check_id, plaintiff_df, use_qdrant=False):
    results = []
    match_items = find_name_in_plaintiff_list(brand_name, parent_company, plaintiff_df)  # Returns either a full item with all details if the brand was found.
    if not match_items:
        match_items = find_brand_in_trademark_text(brand_name, plaintiff_df)

    sim_results = None
    if match_items:
        for match_item in match_items:
            if use_qdrant:
                from Check.RAG.qdrant_search import find_similar_assets_qdrant
                sim_results = await find_similar_assets_qdrant(
                    product_image_path=query_image_part_path,
                    check_id=check_id,
                    client_id=f"check_{check_id}",
                    ip_type="Trademark",
                    plaintiff_df=plaintiff_df,
                    plaintiff_id=match_item["plaintiff_id"],
                    top_n=1,
                    similarity_threshold=0.65
                )
            else:
                sim_results = find_most_similar_trademark_logo(query_image_part_path, match_item["plaintiff_id"], top_n=1, similarity_threshold=0.65)
    if not match_items or not sim_results:  # If we did not find using the plaintiff id, or we dont have a plaintiff id, we search amongst the logo with a higher threshold.
        if use_qdrant:
            from Check.RAG.qdrant_search import find_similar_assets_qdrant
            sim_results = await find_similar_assets_qdrant(
                product_image_path=query_image_part_path,
                check_id=check_id,
                client_id=f"check_{check_id}",
                ip_type="Trademark",
                plaintiff_df=plaintiff_df,
                plaintiff_id=None,
                top_n=1,
                similarity_threshold=0.75
            )
        else:   
            sim_results = find_most_similar_trademark_logo(query_image_part_path, None, top_n=1, similarity_threshold=0.75)  # No match in plaintiff_list, so we search for the brand in trademark_texts.
    
    if sim_results:
        for sim_result in sim_results:
            results.append({
                "type": "trademark",
                "internal_type": {"main_type": "logo", "sub_type": "tro_report_yes"},
                "ip_owner": str(sim_result["plaintiff_name"]),
                "plaintiff_id": str(sim_result["plaintiff_id"]),
                "report": "",
                "risk_level": "高风险",  # in english: "high risk"
                "risk_description": "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼",  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
                "ip_image": [sim_result["filename"]] + sim_result["full_filename"],
                "reg_no": sim_result.get("reg_no", []),
                "int_cls_list": sim_result.get("int_cls_list", []),
                "trademark_text": str(sim_result.get("trademark_text", "")),
                "product_url": [product_url],
                "query_image_path": query_image_path,
            })

    else:
        parts = [part for part in [brand_name, parent_company] if part]
        ip_owner = " - ".join(parts) if parts else "No brand or parent company found"

        results.append({
            "type": "trademark",
            "internal_type": {"main_type": "logo", "sub_type": "no_tro_found"},
            "ip_owner": str(ip_owner),  # Convert to standard string
            "risk_level": "中风险",  # in english: "medium risk"
            "risk_description": "和其他产权有匹配",  # in english: match with other IP
            "product_url": [product_url]
        })

    langfuse.get_client().update_current_span(input=f'query_image_path: \n![Alt text]({product_url.replace(" ", "%20").replace("http", "https")}) \n\nquery_image_part_path: {query_image_part_path} \n\nbrand_name: {brand_name} \n\nparent_company: {parent_company}')

    return results

@observe()
async def process_brand_text(query_image_path, product_url, brand_name, parent_company,  client, bucket, temp_dir, check_id, plaintiff_df):
    results = []
    match_items = find_name_in_plaintiff_list(brand_name, parent_company, plaintiff_df)  # We identify the Plaintiff Name, then look for the brand in trademark_texts. If we found it, then we have a picture, otherwise we have no picture.
    if not match_items:
        match_items = find_brand_in_trademark_text(brand_name, plaintiff_df)  # Allways has picture in the result

    if match_items:
        for match_item in match_items:
            if 'full_filename' in match_item:
                results.append({
                    "type": "trademark",
                    "internal_type": {"main_type": "text", "sub_type": "tro_report_yes"},
                    "ip_owner": str(match_item["plaintiff_name"]),
                    "plaintiff_id": str(match_item["plaintiff_id"]),
                    "report": "",
                    "risk_level": "高风险",  # in english: "high risk"
                    "risk_description": "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼",  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
                    "ip_image": [match_item["filename"]] + match_item["full_filename"],
                    "reg_no": match_item.get("reg_no", []),
                    "int_cls_list": match_item.get("int_cls_list", []),
                    "trademark_text": str(match_item.get("trademark_text", "")),
                    "product_url": [product_url],
                    "query_image_path": query_image_path
                })
            else:
                results.append({
                    "type": "trademark",
                    "internal_type": {"main_type": "text", "sub_type": "tro_no_picture"},  # We matched the plaintiff name, but not the brand in trademark_texts.
                    "ip_owner": str(match_item["plaintiff_name"]),
                    "plaintiff_id": str(match_item["plaintiff_id"]),
                    "risk_level": "高风险",  # in english: "high risk"
                    "risk_description": "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼",  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
                    "reg_no": str(match_item.get("reg_no", "")),  # if found using plaintiff_list, then we have no reg_no
                    "trademark_text": str(match_item.get("trademark_text", "")), # if found using plaintiff_list, then we have no reg_no
                    "product_url": [product_url]
                })

    else:
        parts = [part for part in [brand_name, parent_company] if part]
        ip_owner = " - ".join(parts) if parts else "No brand or parent company found"

        results.append({
            "type": "trademark",
            "internal_type": {"main_type": "text", "sub_type": "no_tro_found"},
            "ip_owner": str(ip_owner),
            "risk_level": "中风险",  # in english: "medium risk"
            "risk_description": "和其他产权有匹配",  # in english: match with other IP
            "product_url": [product_url]
        })

    return results


async def create_trademark_report(temp_dir, check_id, match_item, client, bucket, report_prompt_images, report_prompt_text=None):
    """
    Checks a single Trademark result for infringement.
    """

    IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{int(float(match_item['plaintiff_id']))}/high/{match_item['ip_image'][1]}" # Just take the first certificate?
    ip_file_local = os.path.join(temp_dir, match_item['ip_image'][1])
    download_status = await download_from_url(IP_Url, ip_file_local)
    if not download_status:
        os.makedirs(os.path.join(os.getcwd(), "Errors"), exist_ok=True)
        with open(os.path.join(os.getcwd(), "Errors", "IP_Download_Error.txt"), "a") as f:
            f.write(f"{check_id} - {IP_Url}\n")
        return None

    common_prompt_list = [
        ("text", f'\n\nAfter the report, conclude with regards to the risk of infringement with {{"final_answer": "xx"}} where xx is a score between 0 and 10 where 0 is very low risk, and 10 is very high risk.'),
        ("text", f"\n\nTrademark Registered Image with Registration Number {match_item['reg_no'][0]} and International Classification {match_item['int_cls_list']}:"),
        ("image_path", ip_file_local)
    ]

    prompt_list = []
    if "query_image_path" in match_item:
        prompt_list.append(("text", report_prompt_images))
        prompt_list.extend(common_prompt_list)
        prompt_list.extend([("text", "\n\nProduct Image:"), ("image_path", match_item["query_image_path"])])
        image_url = ["", "", "", IP_Url.replace(" ", "%20").replace("http", "https"), "", match_item["product_url"][0].replace(" ", "%20").replace("http", "https")]

    elif "query_text" in match_item:
        prompt_list.append(("text", report_prompt_text))
        prompt_list.extend(common_prompt_list)
        prompt_list.append(("text", f"\n\nProduct Description: {match_item['query_text']}"))
        image_url = ["", "", "", IP_Url.replace(" ", "%20").replace("http", "https"), ""]

    else:
        print(f" ⚠️⚠️⚠️⚠️ This case should not happen, look why it does: Trademark result with no query_image_path or query_text: {match_item}")

    ai_answer = await vertex_genai_multi_async(prompt_list, image_url=image_url, model_name="gemini-2.0-flash-exp")
    is_trademark = get_json(ai_answer)

    try:
        final_answer_score = int(is_trademark["final_answer"])
    except:
        final_answer_score = 0  # so for "Report not required" we get a score of 0

    if final_answer_score > 0:
        report = get_report(ai_answer, "**1. Mark Information:**", "**End of Report**")
        report = "**Trademark Risk Assessment Report**\n\n" + report
        for filename in match_item["ip_image"]:
            client.copy_object(
                Bucket=bucket,
                Key=f"checks/{check_id}/results/{filename}",
                CopySource={
                    'Bucket': bucket,
                    'Key': f"plaintiff_images/{int(float(match_item['plaintiff_id']))}/high/{filename}",
                    'Region': 'ap-guangzhou'
                }
            )

        match_item["report"] = report
        match_item["risk_level"] = "高风险" if final_answer_score > 5 else "中风险" if final_answer_score > 2 else "低风险"

        return match_item
    else:
        return None

@observe()
def process_keywords(description, ip_keywords, reference_text, client, bucket, temp_dir, check_id, plaintiff_df):
    results = []

    # Key words analysis and description analysis
    text = ""
    if isinstance(ip_keywords, str):
        text += ip_keywords

    if description and isinstance(description, str) and len(description) > 1:
        text += description

    if reference_text and isinstance(reference_text, str) and len(reference_text) > 1:
        text += reference_text

    if text and len(text) > 1:
        # plaintiffs = find_plaintiff_in_text(plaintiff_df, text)
        match_items = find_brand_in_text(text, plaintiff_df)
        for match_item in match_items:
            if 'full_filename' in match_item:
                results.append({
                    "type": "trademark",
                    "internal_type": {"main_type": "keyword", "sub_type": "tro_report_yes"},
                    "ip_owner": str(match_item["plaintiff_name"]),
                    "plaintiff_id": str(match_item["plaintiff_id"]),
                    "report": "",
                    "risk_level": "高风险",  # in english: "high risk"
                    "risk_description": "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼",  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
                    "ip_image": [match_item["filename"]] + match_item["full_filename"],
                    "reg_no": match_item.get("reg_no", []),
                    "int_cls_list": match_item.get("int_cls_list", []),
                    "trademark_text": str(match_item.get("trademark_text", "")),
                    "query_text": text
                })
            else:
                results.append({
                    "type": "trademark",
                    "internal_type": {"main_type": "keyword", "sub_type": "tro_no_picture"},  # We matched the plaintiff name, but not the brand in trademark_texts.
                    "ip_owner": str(match_item["plaintiff_name"]),
                    "plaintiff_id": str(match_item["plaintiff_id"]),
                    "risk_level": "高风险",  # in english: "high risk"
                    "risk_description": "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼",  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
                    "reg_no": str(match_item["reg_no"]),
                    "trademark_text": str(match_item["trademark_text"]),
                    "query_text": text
                })
    return results

@observe()
# @profile
async def check_trademarks(client, bucket, temp_dir, check_id, local_product_images, local_ip_images, local_reference_images, description, ip_keywords, reference_text, plaintiff_df, query_image_urls):

    start_time = time.time()

    trademark_check_tasks = [process_single_image(query_image_path, client, bucket, temp_dir, check_id, plaintiff_df, query_image_urls) for query_image_path in local_product_images+local_ip_images+local_reference_images]
    results = await asyncio.gather(*trademark_check_tasks)
    results = [item for sublist in results for item in sublist if item is not None]

    results.extend(process_keywords(description, ip_keywords, reference_text, client, bucket, temp_dir, check_id, plaintiff_df))


    # if isinstance(ip_keywords, str):
    #     ip_keywords = ip_keywords.split(",")
    # if ip_keywords:
    #     ip_keywords.sort(key=len, reverse=True)
    #     for keyword in ip_keywords:
    #         if len(results) == 0 and len(keyword) > 2: # only add one result if we have none
    #             match_items = find_name_in_plaintiff_list(keyword, keyword, plaintiff_df)
    #             if not match_items:
    #                 match_items = find_brand_in_trademark_text(keyword, plaintiff_df, partial_match = False)

    #             if match_items:
    #                 report_tasks = []
    #                 for match_item in match_items:
    #                     if 'full_filename' in match_item:
    #                         report_tasks.append(create_keyword_report(temp_dir, check_id, query_image_path, product_url, match_item, brand_name, parent_company, plaintiff_df, client, bucket, report_prompt))
    #                     else:
    #                         results.append({
    #                             "type": "keyword",
    #                             "ip_owner": match_item["plaintiff_name"],
    #                             "plaintiff_id": str(match_item["plaintiff_id"]),
    #                             "risk_level": "高风险",  # in english: "high risk"
    #                             "risk_description": "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼",  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
    #                             "product_url": product_url
    #                         })


    # Now it is time to sort out all these results.
    list_of_ip_owners = list(set([result["ip_owner"] for result in results]))
    unique_results = []
    report_tasks = []
    report_indices = []  # Track indices of results with pending reports
    for ip_owner in list_of_ip_owners:
        ip_owner_results = [result for result in results if result["ip_owner"] == ip_owner]
        # Each result["Internal_type"] is a tuple of (main_type, sub_type), where main_type is either "logo" or "text" and sub_type is either "no_tro_found", "tro_no_picture", "tro_report_no" or "tro_report_yes"
        results_logo_tro_report_yes = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "logo" and result["internal_type"]["sub_type"] == "tro_report_yes"]   # We found the ip_owner in plaintiff_list or the brand in trademark_texts.
        results_logo_no_tro_found = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "logo" and result["internal_type"]["sub_type"] == "no_tro_found"]       # We did not find the ip_owner in plaintiff_list or the brand in trademark_texts.
        results_text_tro_report_yes = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "text" and result["internal_type"]["sub_type"] == "tro_report_yes"]   # We found the brand in trademark_texts.
        results_text_tro_no_picture = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "text" and result["internal_type"]["sub_type"] == "tro_no_picture"]   # We matched the plaintiff name, but not the brand in trademark_texts.
        results_text_no_tro_found = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "text" and result["internal_type"]["sub_type"] == "no_tro_found"]       # We did not find the ip_owner in plaintiff_list or the brand in trademark_texts.
        results_keyword_tro_report_yes = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "keyword" and result["internal_type"]["sub_type"] == "tro_report_yes"]   # We found the brand in trademark_texts.
        results_keyword_no_tro_found = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "keyword" and result["internal_type"]["sub_type"] == "no_tro_found"]       # We did not find the ip_owner in plaintiff_list or the brand in trademark_texts.


        ordered_results = results_logo_tro_report_yes + results_text_tro_report_yes + results_text_tro_no_picture + results_logo_no_tro_found + results_text_no_tro_found + results_keyword_tro_report_yes + results_keyword_no_tro_found
        seen_keys = set()

        with open(os.path.join(os.getcwd(), "Check", "Prompts", "Report_Trademark_Images.txt"), "r", encoding="utf-8") as f:
            report_prompt_images = f.read()
        with open(os.path.join(os.getcwd(), "Check", "Prompts", "Report_Trademark_Text.txt"), "r", encoding="utf-8") as f:
            report_prompt_text = f.read()

        for result in ordered_results:
            if ('trademark_text' not in result or result["trademark_text"] == "") and "ip_image" not in result: # No trademark text and no image -> non tro trademark
                if result["ip_owner"] not in seen_keys:
                    unique_results.append(result)
                    seen_keys.add(result["ip_owner"])

            if 'trademark_text' in result and result["trademark_text"] != "" and "ip_image" not in result: # Tro trademark with text but no image. How could this happen?
                langfuse.get_client().update_current_span(meta={"error": f"This case should not happen, look why it does: Trademark result with text but no image: {result}"})
                print(f" ⚠️⚠️⚠️⚠️ This case should not happen, look why it does: Trademark result with text but no image: {result}")
                if result["trademark_text"] not in seen_keys:
                    unique_results.append(result)
                    seen_keys.add(result["ip_owner"])
                    seen_keys.add(result["trademark_text"])

            if 'trademark_text' in result and result["trademark_text"] != "" and "ip_image" in result: # Tro text trademark (with text and image).
                if result["ip_image"][0] not in seen_keys:
                    if result["trademark_text"] not in seen_keys:
                        unique_results.append(result)
                        # Record the index when creating the task
                        report_indices.append(len(unique_results)-1)
                        seen_keys.update([result["ip_owner"], result["trademark_text"], result["ip_image"][0]])
                        report_tasks.append(create_trademark_report(temp_dir, check_id, result, client, bucket, report_prompt_images, report_prompt_text))
                    else: # Add the certificate to the one that has the same text.
                        # These get consolidated during data capture only if they are in the same case => if could still happen that we have 2 certificates for "Channel" that come from different cases.
                        unique_result = [one_result for one_result in unique_results if one_result["trademark_text"] == result["trademark_text"]][0]
                        if unique_result["plaintiff_id"] != result["plaintiff_id"]:
                            langfuse.get_client().update_current_span(meta={"error": f"This case should not happen, look why it does: Trademark result with same text but different plaintiff id: Text = {result['trademark_text']}, Plaintiff ID 1 = {result['plaintiff_id']}, Plaintiff ID 2 = {unique_result['plaintiff_id']}"})
                            print(f" ⚠️⚠️⚠️⚠️ This case should not happen, look why it does: Trademark result with same text but different plaintiff id: Text = {result['trademark_text']}, Plaintiff ID 1 = {result['plaintiff_id']}, Plaintiff ID 2 = {unique_result['plaintiff_id']}")
                        unique_result["ip_image"].extend(result["ip_image"][1:]) # only add the certificates, but the plaintiff id might be different!!!! But so unlikelly given the ip_owner and trademark text are the same
                        for product_url in result["product_url"]:
                            if product_url not in unique_result["product_url"]:
                                unique_result["product_url"].append(product_url)  # Note this will add a querry URL to a result that has already be passed to the report function (because it is a reference)

            if ('trademark_text' not in result or result["trademark_text"] == "") and "ip_image" in result: # Tro logo trademark (with image no text).
                if result["ip_image"][0] not in seen_keys:
                    unique_results.append(result)
                    # Record the index when creating the task
                    report_indices.append(len(unique_results)-1)
                    seen_keys.update([result["ip_owner"], result["ip_image"][0]])
                    report_tasks.append(create_trademark_report(temp_dir, check_id, result, client, bucket, report_prompt_images))

    report_results = await asyncio.gather(*report_tasks)
    for idx, report_result in zip(report_indices, report_results): # Replace results using tracked indices
        unique_results[idx] = report_result
    unique_results = [res for res in unique_results if res is not None]  # Filter out None values (i.e. where the report was not required because not similar at all)

    # for each unique_result, json_dump the ip_image to a file
    # for result in unique_results:
    #     if "ip_image" in result and isinstance(result["ip_image"], list):
    #         result["ip_image"] = json.dumps(result["ip_image"])

    print(f"\033[32m ✅ Trademark: Trademark Analysis DONE, for {len(local_product_images+local_ip_images+local_reference_images)} pictures in {time.time() - start_time:.1f} seconds\033[0m")
    return unique_results