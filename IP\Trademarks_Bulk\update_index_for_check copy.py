# build_and_persist.py  (run in CI or a cron when TM table changes)
import psycopg2, ahocorasick, gzip, pickle, unicodedata, re, traceback
from IP.Trademarks_Bulk.trademark_db import get_db_connection

def update_trademarkindex():
    try: 
        conn = get_db_connection()
        with conn, conn.cursor() as c2:
            # Create the table if it doesn't exist
            c2.execute("""
                CREATE TABLE IF NOT EXISTS trademarks_precomputed_marks (
                    id INTEGER PRIMARY KEY,
                    built_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    automaton_blob BYTEA
                );
            """)
            # Empty the table before inserting new data
            c2.execute("TRUNCATE TABLE trademarks_precomputed_marks;")
            conn.commit() # Commit the TRUNCATE operation

        # SQL command: fet list of trardmark text, without duplicates (keep the one with plaintiff_id, and the most recent one only)
        # Why plaintiff_id IS NULL? In Postgres, FALSE sorts before TRUE, so rows with a plaintiff (FALSE) win over those without.
        sql_query = """
        WITH ranked AS (
        SELECT
            LOWER(mark_text) AS mark_text,
            plaintiff_id,
            reg_no,
            ser_no,
            applicant_name,
            filing_date,
            ROW_NUMBER() OVER (
                PARTITION BY LOWER(mark_text)
                ORDER BY
                    (plaintiff_id IS NULL),
                    filing_date DESC
            ) AS rn
        FROM trademarks
        WHERE mark_text IS NOT NULL AND mark_text <> '' AND mark_feature_code NOT IN (2, 3, 5)
        )
        SELECT mark_text, plaintiff_id, reg_no, ser_no, applicant_name
        FROM ranked
        WHERE rn = 1;
        """
        cur  = conn.cursor(name="tm_cur")     # server-side cursor, streams rows
        cur.execute(sql_query)

        A   = ahocorasick.Automaton()
        meta = {}                              # id → (normalized_mark_text, plaintiff_id, reg_no, ser_no, applicant_name)

        norm = lambda s: re.sub(r'\s+', ' ',
                                unicodedata.normalize('NFKC', s).lower()).strip()

        for idx, row in enumerate(cur):
            mark_text, plaintiff_id, reg_no, ser_no, applicant_name = row
            text = norm(mark_text)
            A.add_word(text, idx)
            meta[idx] = (text, plaintiff_id, reg_no, ser_no, applicant_name)

        A.make_automaton()

        blob = gzip.compress(pickle.dumps((A, meta), protocol=pickle.HIGHEST_PROTOCOL))

        with conn, conn.cursor() as c2:
            c2.execute("""
                INSERT INTO trademarks_precomputed_marks (id, built_at, automaton_blob)
                VALUES (1, now(), %s);
            """, (psycopg2.Binary(blob),))
            conn.commit() # Commit the INSERT operation
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error updating trademark index: {str(e)}, traceback: {traceback.format_exc()}", level='ERROR')
        raise
        
        
if __name__ == "__main__":
    update_trademarkindex()