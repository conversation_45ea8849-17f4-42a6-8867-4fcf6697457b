"""
Reverse check endpoint for comparing IP assets against product images.
"""

import psycopg2, os
from fastapi import APIRouter, Depends
from qdrant_client.http import models
from typing import List, Dict, Any

from models.schemas import ReverseCheckRequest, ReverseCheckResponse, IPAssetInfringement, ProductPointInfringement
from utils.auth import verify_token
from utils.db import get_db_connection
from services.qdrant_service import upsert_ip_assets, query_batch_points
from Common import Constants
from Check.Do_Check_Download import download_from_url
from Check.Create_Report import create_check_report, create_product_url, create_ip_url
import tempfile
import json
import datetime

router = APIRouter()

@router.post("/reverse_check", response_model=ReverseCheckResponse, dependencies=[Depends(verify_token)])
async def reverse_check(request: ReverseCheckRequest):
    """
    Reverse Check endpoint.
    Compare newly added IP assets against the database of user-submitted product images.
    
    Args:
        request: The reverse check request.
        
    Returns:
        The reverse check response.
    """
    # 1. Insert IP Assets into the database

    # Prepare IP assets for upsertion into PostgreSQL
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Consolidated database insertion
    table_mapping = {"Copyright": "copyrights","Patent": "patents_records","Trademark": "trademarks"}
    for ip_asset in request.ip_assets:
        table_name = table_mapping.get(ip_asset.ip_type)
        if table_name:
            columns = ", ".join(ip_asset.metadata.keys())
            placeholders = ", ".join(["%s"] * len(ip_asset.metadata))
            values = list(ip_asset.metadata.values())
            
            cursor.execute(
                f"INSERT INTO {table_name} (id, {columns}) VALUES (%s, {placeholders}) "
                f"ON CONFLICT (id) DO UPDATE SET {', '.join([f'{k} = EXCLUDED.{k}' for k in ip_asset.metadata.keys()])}",
                [ip_asset.id] + values
            )
            conn.commit()
    
    # Upsert IP assets into IP_Assets collection
    upsert_ip_assets(request.ip_assets)
    
    
    # 2. Querry Qdrant for similar Product Images
    # Prepare batch queries for Product_Images collection
    query_requests = []
    
    # Configuration for different IP types' query parameters
    IP_TYPE_QUERY_CONFIG = {"Copyright": {"threshold": 0.75, "limit": 300},
                            "Patent": {"threshold": 0.8, "limit": 300},
                            "Trademark": {"threshold": 0.7, "limit": 300}}

    querried_ip_assets = []
    for ip_asset in request.ip_assets:
        ip_type = ip_asset.ip_type
        if ip_asset.siglip_vector and len(ip_asset.siglip_vector) == 1024:
            query_requests.append(models.QueryRequest(
                query_vector=("siglip_vector", ip_asset.siglip_vector),
                limit=IP_TYPE_QUERY_CONFIG[ip_type]["limit"],
                score_threshold=IP_TYPE_QUERY_CONFIG[ip_type]["threshold"],
                with_payload=models.PayloadSelectorInclude(include=["client_id", "check_id", "filename"]),
                with_vectors=False
            ))
            querried_ip_assets.append(ip_asset)
    
    # Execute batch query
    batch_results = query_batch_points("Product_Images", query_requests)
    
    
    # 3. Run the reports and filter the results
    infringements = []
    
    # For each IP_Asset
    with tempfile.TemporaryDirectory() as temp_dir:
        for query_index, ip_asset in enumerate(querried_ip_assets):
            # Download the IP Asset image? Not it is already local.
            ip_asset_path, ip_url = get_ip_asset_path(ip_asset)
            
            # For each match of a single IP_Asset
            non_match = 0
            for scored_point in batch_results[query_index]:
                product_image_path = await download_product_image(temp_dir, scored_point.payload["check_id"], scored_point.payload["filename"])
                
                result = await create_check_report(
                    ip_type=ip_type,
                    check_id=scored_point.payload["check_id"],
                    result={
                        'ip_owner': ip_asset.metadata["ip_owner"],
                        'reg_no': ip_asset.metadata["reg_no"],
                        'product_local_path': product_image_path,
                        'ip_local_paths': [ip_asset_path]
                    }
                )
                
                if result["risk_score"] <= 2:
                    non_match += 1
                else:
                    non_match = 0
                    
                    # Put the result in the database
                    infringements.append({
                        "date_added": datetime.now().date(),
                        "client_id": scored_point.payload["client_id"],
                        "check_id": scored_point.payload["check_id"],
                        "results": [result]
                        })
                    
                if non_match >= 5:
                    break
                
            # Consolidate by check_id: i.e. if one IP match against picture_1 and picture_3 of the same check_id, we want toconsolidate the results
            check_ids_seen = []
            infringements_to_remove = []
            for inf in infringements:
                check_id = inf["check_id"]
                if check_id not in check_ids_seen:
                    check_ids_seen.append(check_id)
                else:
                    existing_inf = next((inf for inf in infringements if inf["check_id"] == check_id), None)
                    inf["results"].extend(existing_inf["results"])
                    infringements_to_remove(existing_inf)
            infringements = [inf for inf in infringements if inf not in infringements_to_remove]
 

            # Upload to database
            conn = get_db_connection()
            cursor = conn.cursor()
            for inf in infringements:
                cursor.execute(
                    "INSERT INTO reverse_check_result (date_added, client_id, check_id, results) VALUES (%s, %s, %s)",
                    (inf["date_added"], inf["client_id"], inf["check_id"], json.dumps(inf["results"]))
                )
            conn.commit()
            
    cursor.close()
    conn.close()


async def download_product_image(temp_dir, check_id, filename):
    product_image_path = os.path.join(temp_dir, filename)
    product_image_url = create_product_url(check_id, product_image_path)
    await download_from_url(product_image_url, product_image_path)
    return product_image_path

def get_ip_asset_path(ip_asset):
    if ip_asset.ip_type == "Copyright":
        ip_asset_path =  os.path.join(Constants.local_ip_folder, "Copyrights", ip_asset.metadata["reg_no"] + ".webp")
        ip_url = create_ip_url(ip_asset.ip_type, ip_asset.metadata["reg_no"])
    elif ip_asset.ip_type == "Trademark":
        ip_asset_path = os.path.join(Constants.local_ip_folder, "Trademarks", "USPTO_Daily" , "Images", ip_asset.metadata["ser_no"][-2:], ip_asset.metadata["ser_no"][-4:-2], ip_asset.metadata["ser_no"] + ".webp")
        ip_url = create_ip_url(ip_asset.ip_type, ip_asset.metadata["ser_no"])
    elif ip_asset.ip_type == "Patent":
        ip_asset_path = os.path.join(Constants.local_ip_folder, "Patents", "Images", ip_asset.metadata["reg_no"] + ".webp")
        ip_url = create_ip_url(ip_asset.ip_type, ip_asset.metadata["reg_no"])
    
    return ip_asset_path, ip_url


async def get_reverse_check_report(ip_type, check_id, ip_owner, reg_no, ip_file_local, product_local_path, ip_url, product_check_id, product_filename):
    # Create a result dictionary that matches the expected format
    result = {
        'ip_owner': ip_owner,
        'reg_no': reg_no,
        'product_local_path': product_local_path,
        'ip_local_paths': [ip_file_local],
        'IP_Urls': [ip_url],
        'download_statuses': [True],  # Assume files are already downloaded
        'ip_image': []  # Will be populated if needed
    }

    # Import here to avoid circular imports
    

    return await create_check_report(
        ip_type=ip_type,
        check_id=check_id,
        result=result
    )