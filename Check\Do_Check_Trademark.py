from Check.Find_In_List import find_brand_in_trademark_text, find_name_in_plaintiff_list, find_brand_in_text
from langfuse import observe
import langfuse

from AI.GCV_GetImageParts import get_image_parts_async
import shutil, time, os, asyncio
import Common.Constants as Constants
from Check.RAG.qdrant_search import find_similar_assets_qdrant, prepare_ip_downloads
from Check.Create_Report import create_check_report, create_product_url


@observe()
async def process_single_image(client_name, query_image_path, check_id, plaintiff_df):
    prompt_brand = f'Detect all of the Trademarks of generally known brands. For each, assign a label that includes the type of trademark (text or logo), the brand name and the parent company in this format: "Type, Brand Name, Parent Company", where Type can only be text or logo.'

    # For Langfuse
    product_url = create_product_url(check_id, query_image_path)
    image_url = ["", product_url.replace(" ", "%20").replace("http", "https")]

    # We make a copy just for trademark because copyright will also create parts and it should not overwrite the copyright parts
    query_image_path_old = query_image_path
    query_image_path = os.path.join(os.path.dirname(query_image_path), "trademark", os.path.basename(query_image_path))
    os.makedirs(os.path.dirname(query_image_path), exist_ok=True)
    shutil.copy(query_image_path_old, query_image_path)
    images_parts = await get_image_parts_async(prompt_brand, query_image_path, image_url, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX) #, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX)

    results = []
    for image_part in images_parts:
        label_parts = image_part["label"].split(",")
        label_type = label_parts[0].lower().strip() if len(label_parts) >= 3 else "unknown"
        brand_name = label_parts[1].strip() if len(label_parts) >= 3 else ""
        parent_company = label_parts[2].strip() if len(label_parts) >= 3 else ""
        if label_type == "logo":
            results.extend(await process_brand_logo(client_name, image_part["path"], product_url, brand_name, parent_company, check_id, plaintiff_df))
        elif label_type == "text":
            results.extend(await process_brand_text(query_image_path, product_url, brand_name, parent_company, plaintiff_df))
        else:
            print(f"Trademark: Unknown label: {image_part['label']}")

    langfuse.get_client().update_current_span(input=f'\n![Alt text]({product_url.replace(" ", "%20").replace("http", "https")})')

    # await upload_task
    return results

@observe()
async def process_brand_logo(client_name, query_image_part_path, product_url, brand_name, parent_company, check_id, plaintiff_df):
    results = []
    match_items = find_name_in_plaintiff_list(brand_name, parent_company, plaintiff_df)  # Returns either a full item with all details if the brand was found.
    if not match_items:
        match_items = find_brand_in_trademark_text(brand_name, plaintiff_df)

    sim_results = None
    if match_items:
        for match_item in match_items:
            sim_results = await find_similar_assets_qdrant(
                query_image_paths=[query_image_part_path],
                check_id=check_id,
                client_id=client_name,
                ip_type="Trademark",
                plaintiff_df=plaintiff_df,
                plaintiff_id=match_item["plaintiff_id"],
                top_n=1,
                similarity_threshold=0.65
            )
    if not match_items or not sim_results:  # If we did not find using the plaintiff id, or we dont have a plaintiff id, we search amongst the logo with a higher threshold.
        # No match in plaintiff_list, so we search for the brand in trademark_texts.
        sim_results = await find_similar_assets_qdrant(  # this will set 
            query_image_paths=[query_image_part_path],
            check_id=check_id,
            client_id=client_name,
            ip_type="Trademark",
            plaintiff_df=plaintiff_df,
            plaintiff_id=None,
            top_n=1,
            similarity_threshold=0.75
        )
    
    if sim_results:
        for sim_result in sim_results:
            sim_result["internal_type"] = {"main_type": "logo", "sub_type": "tro_report_yes"}
            sim_result["report"] = ""
            sim_result["risk_level"] = "高风险"  # in english: "high risk"
            sim_result["risk_description"] = "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼"  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
            sim_result["product_url"] = [product_url]
            results.append(sim_result)

    else:
        parts = [part for part in [brand_name, parent_company] if part]
        ip_owner = " - ".join(parts) if parts else "No brand or parent company found"

        results.append({
            "ip_type": "Trademark",
            "internal_type": {"main_type": "logo", "sub_type": "no_tro_found"},
            "ip_owner": str(ip_owner),  # Convert to standard string
            "risk_level": "中风险",  # in english: "medium risk"
            "risk_description": "和其他产权有匹配",  # in english: match with other IP
            "product_url": [product_url]
        })

    langfuse.get_client().update_current_span(input=f'query_image_path: \n![Alt text]({product_url.replace(" ", "%20").replace("http", "https")}) \n\nquery_image_part_path: {query_image_part_path} \n\nbrand_name: {brand_name} \n\nparent_company: {parent_company}')

    return results

@observe()
async def process_brand_text(query_image_path, product_url, brand_name, parent_company,  plaintiff_df):
    results = []
    match_items = find_name_in_plaintiff_list(brand_name, parent_company, plaintiff_df)  # We identify the Plaintiff Name, then look for the brand in trademark_texts. If we found it, then we have a picture, otherwise we have no picture.
    if not match_items:
        match_items = find_brand_in_trademark_text(brand_name, plaintiff_df)  # Allways has picture in the result

    if match_items:
        for match_item in match_items:
            match_item["ip_type"] = "Trademark"
            match_item["risk_level"] = "高风险"  # in english: "high risk"
            match_item["risk_description"] = "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼"  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
            match_item["product_url"] = [product_url]
            match_item["product_local_path"] = query_image_path
            match_item["ip_image"] = [match_item["filename"]] + match_item["full_filename"]
            
            if 'full_filename' in match_item:
                match_item["internal_type"] = {"main_type": "text", "sub_type": "tro_report_yes"}
                match_item["report"] = ""
                match_item["ip_image"] = [match_item["filename"]] + match_item["full_filename"]
            else:
                match_item["internal_type"] = {"main_type": "text", "sub_type": "tro_no_picture"}
            
            results.append(match_item)

    else:
        parts = [part for part in [brand_name, parent_company] if part]
        ip_owner = " - ".join(parts) if parts else "No brand or parent company found"

        results.append({
            "ip_type": "Trademark",
            "internal_type": {"main_type": "text", "sub_type": "no_tro_found"},
            "ip_owner": str(ip_owner),
            "risk_level": "中风险",  # in english: "medium risk"
            "risk_description": "和其他产权有匹配",  # in english: match with other IP
            "product_url": [product_url]
        })

    return results

@observe()
def process_keywords(description, ip_keywords, reference_text, plaintiff_df):
    results = []

    # Key words analysis and description analysis
    text = ""
    if isinstance(ip_keywords, str):
        text += ip_keywords

    if description and isinstance(description, str) and len(description) > 1:
        text += description

    if reference_text and isinstance(reference_text, str) and len(reference_text) > 1:
        text += reference_text

    if text and len(text) > 1:
        # plaintiffs = find_plaintiff_in_text(plaintiff_df, text)
        match_items = find_brand_in_text(text, plaintiff_df)
        for match_item in match_items:
            match_item["ip_type"] = "Trademark"
            match_item["risk_level"] = "高风险"  # in english: "high risk"
            match_item["risk_description"] = "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼"  # in english: "and the IP or IP owner has previously filed a TRO lawsuit"
            match_item["ip_image"] = [match_item["filename"]] + match_item["full_filename"]
            match_item["query_text"] = text
            
            if 'full_filename' in match_item:
                match_item["internal_type"] = {"main_type": "keyword", "sub_type": "tro_report_yes"}
                match_item["report"] = ""
                match_item["ip_image"] = [match_item["filename"]] + match_item["full_filename"]
            else:
                match_item["internal_type"] = {"main_type": "keyword", "sub_type": "tro_no_picture"}
            
            results.append(match_item)
    return results


@observe()
# @profile
async def check_trademarks(client, bucket, temp_dir, client_name, check_id, local_product_images, local_client_ip_images, local_reference_images, description, ip_keywords, reference_text, plaintiff_df):

    start_time = time.time()

    trademark_check_tasks = [process_single_image(client_name, query_image_path, check_id, plaintiff_df) for query_image_path in local_product_images+local_client_ip_images+local_reference_images]
    results = await asyncio.gather(*trademark_check_tasks)
    results = [item for sublist in results for item in sublist if item is not None]

    results.extend(process_keywords(description, ip_keywords, reference_text, plaintiff_df))

    # Now it is time to sort out all these results.
    list_of_ip_owners = list(set([result["ip_owner"] for result in results]))
    unique_results = []
    report_tasks = []
    report_indices = []  # Track indices of results with pending reports
    for ip_owner in list_of_ip_owners:
        ip_owner_results = [result for result in results if result["ip_owner"] == ip_owner]
        # Each result["Internal_type"] is a tuple of (main_type, sub_type), where main_type is either "logo" or "text" and sub_type is either "no_tro_found", "tro_no_picture", "tro_report_no" or "tro_report_yes"
        results_logo_tro_report_yes = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "logo" and result["internal_type"]["sub_type"] == "tro_report_yes"]   # We found the ip_owner in plaintiff_list or the brand in trademark_texts.
        results_logo_no_tro_found = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "logo" and result["internal_type"]["sub_type"] == "no_tro_found"]       # We did not find the ip_owner in plaintiff_list or the brand in trademark_texts.
        results_text_tro_report_yes = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "text" and result["internal_type"]["sub_type"] == "tro_report_yes"]   # We found the brand in trademark_texts.
        results_text_tro_no_picture = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "text" and result["internal_type"]["sub_type"] == "tro_no_picture"]   # We matched the plaintiff name, but not the brand in trademark_texts.
        results_text_no_tro_found = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "text" and result["internal_type"]["sub_type"] == "no_tro_found"]       # We did not find the ip_owner in plaintiff_list or the brand in trademark_texts.
        results_keyword_tro_report_yes = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "keyword" and result["internal_type"]["sub_type"] == "tro_report_yes"]   # We found the brand in trademark_texts.
        results_keyword_no_tro_found = [result for result in ip_owner_results if result["internal_type"]["main_type"] == "keyword" and result["internal_type"]["sub_type"] == "no_tro_found"]       # We did not find the ip_owner in plaintiff_list or the brand in trademark_texts.


        ordered_results = results_logo_tro_report_yes + results_text_tro_report_yes + results_text_tro_no_picture + results_logo_no_tro_found + results_text_no_tro_found + results_keyword_tro_report_yes + results_keyword_no_tro_found
        seen_keys = set()

        for result in ordered_results:
            if ('text' not in result or result["text"] == "") and "ip_local_paths" not in result: # No trademark text and no image -> non tro trademark
                if result["ip_owner"] not in seen_keys:
                    unique_results.append(result)
                    seen_keys.add(result["ip_owner"])

            if 'text' in result and result["text"] != "" and "ip_local_paths" not in result: # Tro trademark with text but no image. How could this happen?
                langfuse.get_client().update_current_span(meta={"error": f"This case should not happen, look why it does: Trademark result with text but no image: {result}"})
                print(f" ⚠️⚠️⚠️⚠️ This case should not happen, look why it does: Trademark result with text but no image: {result}")
                if result["text"] not in seen_keys:
                    unique_results.append(result)
                    seen_keys.add(result["ip_owner"])
                    seen_keys.add(result["text"])

            if 'text' in result and result["text"] != "" and "ip_local_paths" in result: # Tro text trademark (with text and image).
                if result["ip_local_paths"][0] not in seen_keys:
                    if result["text"] not in seen_keys:
                        unique_results.append(result)
                        # Record the index when creating the task
                        report_indices.append(len(unique_results)-1)
                        seen_keys.update([result["ip_owner"], result["text"], result["ip_local_paths"][0]])
                        report_tasks.append(create_check_report("Trademark", check_id, result, client, bucket))
                    else: # Add the certificate to the one that has the same text.
                        # These get consolidated during data capture only if they are in the same case => if could still happen that we have 2 certificates for "Channel" that come from different cases.
                        unique_result = [one_result for one_result in unique_results if one_result["text"] == result["text"]][0]
                        if unique_result["plaintiff_id"] != result["plaintiff_id"]:
                            langfuse.get_client().update_current_span(meta={"error": f"This case should not happen, look why it does: Trademark result with same text but different plaintiff id: Text = {result['text']}, Plaintiff ID 1 = {result['plaintiff_id']}, Plaintiff ID 2 = {unique_result['plaintiff_id']}"})
                            print(f" ⚠️⚠️⚠️⚠️ This case should not happen, look why it does: Trademark result with same text but different plaintiff id: Text = {result['text']}, Plaintiff ID 1 = {result['plaintiff_id']}, Plaintiff ID 2 = {unique_result['plaintiff_id']}")
                        unique_result["ip_local_paths"].extend(result["ip_local_paths"][1:]) # only add the certificates, but the plaintiff id might be different!!!! But so unlikelly given the ip_owner and trademark text are the same
                        for product_url in result["product_url"]:
                            if product_url not in unique_result["product_url"]:
                                unique_result["product_url"].append(product_url)  # Note this will add a querry URL to a result that has already be passed to the report function (because it is a reference)

            if ('text' not in result or result["text"] == "") and "ip_local_paths" in result: # Tro logo trademark (with image no text).
                if result["ip_local_paths"][0] not in seen_keys:
                    unique_results.append(result)
                    # Record the index when creating the task
                    report_indices.append(len(unique_results)-1)
                    seen_keys.update([result["ip_owner"], result["ip_local_paths"][0]])
                    report_tasks.append(create_check_report("Trademark", check_id, result, client, bucket))

    # Prepare downloads for results that need reports
    results_needing_reports = [unique_results[idx] for idx in report_indices]
    if results_needing_reports:
        enhanced_results = await prepare_ip_downloads(results_needing_reports, temp_dir, check_id)
        # Update the unique_results with enhanced download information
        for idx, enhanced_result in zip(report_indices, enhanced_results):
            unique_results[idx] = enhanced_result

    report_results = await asyncio.gather(*report_tasks)
    for idx, report_result in zip(report_indices, report_results): # Replace results using tracked indices
        unique_results[idx] = report_result
    unique_results = [res for res in unique_results if res is not None]  # Filter out None values (i.e. where the report was not required because not similar at all)

    print(f"\033[32m ✅ Trademark: Trademark Analysis DONE, for {len(local_product_images+local_client_ip_images+local_reference_images)} pictures in {time.time() - start_time:.1f} seconds\033[0m")
    return unique_results