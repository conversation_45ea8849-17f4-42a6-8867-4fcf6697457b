from DatabaseManagement.ImportExport import get_table_from_GZ
import time
from datetime import datetime
import json
import pandas as pd
import psycopg2, gzip, pickle, re, unicodedata, ahocorasick
import sys
from pympler import asizeof
from IP.Trademarks_Bulk.trademark_db import get_db_connection

# Global variables for caching DataFrames
cached_plaintiff_df = None
cached_cases_df = None
brand_list = None
last_cache_update = None
TM_AUTO = None
TM_META = None

def update_dataframe_cache():
    """Updates the cached DataFrames from the database."""
    global cached_plaintiff_df
    global cached_cases_df
    global brand_list
    global last_cache_update
    global TM_AUTO
    global TM_META

    print("Updating DataFrame cache...")
    start_time = time.time()
    cached_plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)
    cached_cases_df = get_table_from_GZ("tb_case", force_refresh=False)
    # Ensure images is not None
    cached_cases_df["images"] = cached_cases_df["images"].apply(lambda x: x if x is not None else {"trademarks":{}, "copyrights":{}, "patents":{}})
    brand_list = create_brand_list(cached_cases_df)
    TM_AUTO, TM_META = load_tm_automaton()
    last_cache_update = datetime.now()
    end_time = time.time()
    print(f"DataFrame cache updated in {end_time - start_time:.1f} seconds.")


def create_brand_list(cases_df):
    brand_list = []
    for i, row in cases_df.iterrows():
        images = row["images"]
        if pd.isna(row["plaintiff_id"]) or row["plaintiff_id"] in ["", None]: # If it is not a TRO case, we skip it.
            continue
        plaintiff_id = int(float(row["plaintiff_id"]))
        if isinstance(images, str):
            # Parse JSON if it's a string but not compressed
            images = json.loads(images)
        elif not isinstance(images, dict):
            # Handle None or other unexpected types
            images = {"trademarks": {}, "patents": {}, "copyrights": {}}

        if images:
            seen_brands = set()
            for trademark_image in images['trademarks']:
                data = images['trademarks'][trademark_image]
                if 'trademark_text' in data:
                    if data['trademark_text'][0].lower() != "" and (plaintiff_id,data['trademark_text'][0].lower()) not in seen_brands:
                        seen_brands.add((plaintiff_id,data['trademark_text'][0].lower())) # To avoid duplicates

                        brand_list.append({
                            "plaintiff_id": plaintiff_id,
                            "trademark_text": data['trademark_text'][0].lower(),
                            "filename": trademark_image,
                            "full_filename": data.get("full_filename"),
                            "reg_no": data.get("reg_no"),
                            "int_cls_list": data.get("int_cls_list")
                        })
                else:
                    print(f"\033[91mNo trademark text found: {row['docket']}\033[0m")

    return brand_list

def load_tm_automaton():
    conn = get_db_connection()
    cur  = conn.cursor()
    cur.execute("SELECT automaton_blob FROM trademarks_precomputed_marks WHERE id = 1")
    (gz_blob,) = cur.fetchone()
    A, meta = pickle.loads(gzip.decompress(gz_blob))
    print(f"RAM usage of A: {asizeof.asizeof(A) / (1024*1024):.2f} MB")
    print(f"RAM usage of meta: {asizeof.asizeof(meta) / (1024*1024):.2f} MB")
    cur.close()
    conn.close()
    return A, meta

norm = lambda s: re.sub(r'\s+', ' ', unicodedata.normalize('NFKC', s).lower()).strip()


def get_cached_plaintiff_df():
    # global cached_plaintiff_df
    """Returns the cached plaintiff DataFrame."""
    if cached_plaintiff_df is None:
        update_dataframe_cache()
    return cached_plaintiff_df

def get_cached_cases_df():
    # global cached_cases_df
    """Returns the cached cases DataFrame."""
    if cached_cases_df is None:
        update_dataframe_cache()
    return cached_cases_df

def get_cached_brand_list():
    # global brand_list
    """Returns the cached brand list."""
    if brand_list is None:
        update_dataframe_cache()
    return brand_list